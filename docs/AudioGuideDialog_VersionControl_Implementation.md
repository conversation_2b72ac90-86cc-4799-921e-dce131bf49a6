# AudioGuideDialog 版本控制自动弹窗功能实现文档

## 功能概述

在 `NormalRecordingListFragment` 页面实现了版本控制的 `AudioGuideDialog` 自动弹窗功能，确保每个应用版本只显示一次引导对话框。

## 实现详情

### 1. 核心功能

- **版本控制**: 基于应用版本号控制对话框显示
- **一次性显示**: 每个版本只在首次进入页面时显示一次
- **版本升级支持**: 应用升级后会重新显示对话框
- **本地存储**: 使用 SharedPreferences 记录显示状态

### 2. 技术实现

#### 2.1 存储机制

```kotlin
// 存储键格式: "audio_guide_dialog_shown_v{版本号}"
private const val AUDIO_GUIDE_DIALOG_SHOWN_KEY = "audio_guide_dialog_shown_v"

// 示例: 版本100的存储键为 "audio_guide_dialog_shown_v100"
val versionKey = "${AUDIO_GUIDE_DIALOG_SHOWN_KEY}${currentVersionCode}"
```

#### 2.2 版本检查逻辑

```kotlin
private fun checkAndShowAudioGuideDialog() {
    try {
        val currentVersionCode = getCurrentVersionCode()
        val sharedPrefs = requireContext().getSharedPreferences(
            ConstsConfig.AppConfigKey,
            Context.MODE_PRIVATE
        )

        val versionKey = "${AUDIO_GUIDE_DIALOG_SHOWN_KEY}${currentVersionCode}"
        val hasShownForCurrentVersion = sharedPrefs.getBoolean(versionKey, false)

        if (!hasShownForCurrentVersion) {
            // 显示对话框并记录状态
            isShowAudioGuide.value = true
            sharedPrefs.edit().putBoolean(versionKey, true).apply()
            Timber.d("AudioGuideDialog shown for version: $currentVersionCode")
        } else {
            Timber.d("AudioGuideDialog already shown for version: $currentVersionCode")
        }
    } catch (e: Exception) {
        Timber.e(e, "Error checking audio guide dialog version")
    }
}

private fun getCurrentVersionCode(): Int {
    return try {
        val packageInfo = requireContext().packageManager.getPackageInfo(
            requireContext().packageName,
            0
        )
        packageInfo.versionCode
    } catch (e: PackageManager.NameNotFoundException) {
        Timber.e(e, "Failed to get version code")
        -1
    }
}
```

#### 2.3 触发时机

对话框检查在 `onViewCreated` 生命周期方法中调用：

```kotlin
override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
    super.onViewCreated(view, savedInstanceState)
    // ... 其他初始化代码
    
    // 检查是否需要显示音频引导对话框
    checkAndShowAudioGuideDialog()
}
```

### 3. 使用场景

#### 3.1 首次安装应用
- 用户首次进入 `NormalRecordingListFragment` 页面
- 自动显示 `AudioGuideDialog`
- 记录当前版本已显示状态

#### 3.2 同版本再次进入
- 检查当前版本是否已显示过
- 如已显示，不再弹出对话框

#### 3.3 应用版本升级
- 检测到新版本号
- 重新显示引导对话框
- 记录新版本的显示状态

### 4. 存储特性

#### 4.1 设备本地存储
- 使用 `SharedPreferences` 存储在设备本地
- 存储路径: `ConstsConfig.AppConfigKey`
- 与用户账号无关，基于设备

#### 4.2 版本隔离
- 每个版本使用独立的存储键
- 版本升级不会影响新版本的显示逻辑
- 支持版本回退场景

### 5. 兼容性

#### 5.1 现有功能保持不变
- 右上角手动触发按钮功能不受影响
- 现有的 `AudioGuideDialog` 组件无需修改
- 不影响其他页面的对话框使用

#### 5.2 错误处理
- 版本号获取失败时的异常处理
- SharedPreferences 操作异常处理
- 日志记录便于调试

### 6. 测试验证

#### 6.1 单元测试
- 版本控制逻辑测试
- 存储键格式验证
- 异常情况处理测试

#### 6.2 集成测试场景
1. 首次安装应用，进入页面验证对话框显示
2. 同版本再次进入，验证对话框不显示
3. 模拟版本升级，验证对话框重新显示
4. 手动触发按钮功能验证

### 7. 配置说明

#### 7.1 依赖项
- `AppEnvironment.getVersionCode()`: 获取应用版本号
- `ConstsConfig.AppConfigKey`: SharedPreferences 存储键
- `Context.getSharedPreferences()`: 本地存储访问

#### 7.2 常量配置
```kotlin
companion object {
    private const val AUDIO_GUIDE_DIALOG_SHOWN_KEY = "audio_guide_dialog_shown_v"
}
```

### 8. 维护说明

#### 8.1 日志监控
- 对话框显示状态日志
- 版本检查过程日志
- 异常情况错误日志

#### 8.2 数据清理
- 旧版本的存储数据会自然保留
- 如需清理，可通过 SharedPreferences 手动删除对应键值

## 实现状态

### ✅ 已完成
- [x] 版本控制逻辑实现
- [x] SharedPreferences 存储机制
- [x] 自动弹窗功能集成
- [x] 错误处理和日志记录
- [x] 单元测试编写
- [x] 测试依赖配置
- [x] 编译验证通过

### 📋 测试状态
- [x] 单元测试编写完成
- [x] 测试依赖添加完成
- [x] 编译测试通过
- [ ] 手动集成测试（需要在实际设备上验证）

### 🔧 技术细节
- **实现文件**: `feature_miwear_speechhub/src/main/java/com/superhexa/supervision/feature/miwear/speechhub/presentation/recording/NormalRecordingListFragment.kt`
- **测试文件**: `feature_miwear_speechhub/src/test/java/com/superhexa/supervision/feature/miwear/speechhub/presentation/recording/AudioGuideDialogVersionControlTest.kt`
- **版本检测**: 使用 `PackageManager.getPackageInfo()` 获取应用版本
- **存储方式**: SharedPreferences 本地存储
- **触发时机**: Fragment 的 `onViewCreated()` 生命周期方法

## 总结

该实现方案完全满足需求，提供了稳定可靠的版本控制机制，确保用户体验的同时保持代码的简洁性和可维护性。功能已完全实现并通过编译测试，可以进行代码审查和集成到主分支。
