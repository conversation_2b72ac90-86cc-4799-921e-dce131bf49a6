package com.superhexa.supervision.feature.miwear.speechhub.presentation.recording

import android.content.Context
import android.content.SharedPreferences
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import io.mockk.*
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * AudioGuideDialog 版本控制功能测试
 * 测试版本控制的自动弹窗逻辑
 * 创建日期: 2025/8/1
 * 作者: AI Assistant
 */
class AudioGuideDialogVersionControlTest {

    private lateinit var mockContext: Context
    private lateinit var mockSharedPrefs: SharedPreferences
    private lateinit var mockEditor: SharedPreferences.Editor
    private lateinit var mockPackageManager: PackageManager
    private lateinit var mockPackageInfo: PackageInfo

    @Before
    fun setup() {
        mockContext = mockk()
        mockSharedPrefs = mockk()
        mockEditor = mockk()
        mockPackageManager = mockk()
        mockPackageInfo = mockk()

        every { mockContext.getSharedPreferences(ConstsConfig.AppConfigKey, Context.MODE_PRIVATE) } returns mockSharedPrefs
        every { mockSharedPrefs.edit() } returns mockEditor
        every { mockEditor.putBoolean(any(), any()) } returns mockEditor
        every { mockEditor.apply() } just Runs
        every { mockContext.packageManager } returns mockPackageManager
        every { mockContext.packageName } returns "com.test.app"
        every { mockPackageManager.getPackageInfo("com.test.app", 0) } returns mockPackageInfo
    }

    @Test
    fun `test first time show dialog for new version`() {
        // Given
        val versionCode = 100
        val versionKey = "audio_guide_dialog_shown_v$versionCode"

        mockPackageInfo.versionCode = versionCode
        every { mockSharedPrefs.getBoolean(versionKey, false) } returns false

        // When
        val shouldShow = !mockSharedPrefs.getBoolean(versionKey, false)

        // Then
        assertTrue("Should show dialog for new version", shouldShow)

        // Verify that the flag would be set
        verify(exactly = 0) { mockEditor.putBoolean(versionKey, true) }
    }

    @Test
    fun `test do not show dialog for already shown version`() {
        // Given
        val versionCode = 100
        val versionKey = "audio_guide_dialog_shown_v$versionCode"

        mockPackageInfo.versionCode = versionCode
        every { mockSharedPrefs.getBoolean(versionKey, false) } returns true

        // When
        val shouldShow = !mockSharedPrefs.getBoolean(versionKey, false)

        // Then
        assertFalse("Should not show dialog for already shown version", shouldShow)
    }

    @Test
    fun `test show dialog again for new version after upgrade`() {
        // Given - simulate version upgrade
        val oldVersionCode = 100
        val newVersionCode = 101
        val oldVersionKey = "audio_guide_dialog_shown_v$oldVersionCode"
        val newVersionKey = "audio_guide_dialog_shown_v$newVersionCode"

        // Old version was shown
        every { mockSharedPrefs.getBoolean(oldVersionKey, false) } returns true
        // New version not shown yet
        every { mockSharedPrefs.getBoolean(newVersionKey, false) } returns false
        mockPackageInfo.versionCode = newVersionCode

        // When
        val shouldShow = !mockSharedPrefs.getBoolean(newVersionKey, false)

        // Then
        assertTrue("Should show dialog for new version after upgrade", shouldShow)
    }

    @Test
    fun `test version key format`() {
        // Given
        val versionCode = 12345
        val expectedKey = "audio_guide_dialog_shown_v12345"
        
        // When
        val actualKey = "audio_guide_dialog_shown_v$versionCode"
        
        // Then
        assertEquals("Version key format should be correct", expectedKey, actualKey)
    }

    @Test
    fun `test multiple version scenarios`() {
        // Test scenario: v100 -> v101 -> v102
        val versions = listOf(100, 101, 102)

        versions.forEach { version ->
            val versionKey = "audio_guide_dialog_shown_v$version"
            mockPackageInfo.versionCode = version
            every { mockSharedPrefs.getBoolean(versionKey, false) } returns false

            val shouldShow = !mockSharedPrefs.getBoolean(versionKey, false)
            assertTrue("Should show dialog for version $version", shouldShow)
        }
    }

    @Test
    fun `test error handling for invalid version code`() {
        // Given
        mockPackageInfo.versionCode = -1

        // When
        val versionCode = mockPackageInfo.versionCode
        val versionKey = "audio_guide_dialog_shown_v$versionCode"

        // Then
        assertEquals("Should handle invalid version code", "audio_guide_dialog_shown_v-1", versionKey)
    }
}
