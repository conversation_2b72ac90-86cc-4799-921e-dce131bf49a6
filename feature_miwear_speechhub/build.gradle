plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'kotlin-parcelize'
}

/**
 *  ARouter配置 kotlin 模块 需要放到和android 同级，
 *  参考https://github.com/alibaba/ARouter/blob/master/module-kotlin/build.gradle
 */
kapt {
    arguments {
        arg("AROUTER_MODULE_NAME", project.getName())
    }
}

apply from: "../moduleFlavor.gradle"
apply from: "../library.gradle"

android {
    namespace 'com.superhexa.supervision.feature.miwear.speechhub'

    composeOptions {
        kotlinCompilerExtensionVersion "$versions.compose_version"
    }
    buildFeatures {
        viewBinding true
        compose true
    }
}

dependencies {
    api project(path: ':module_basic:library_base')
    api project(path: ':module_basic:library_string')
    api project(':module_basic:library_component')
    api project(':module_basic:library_miavis_capability')
    api project(':module_basic:library_statistic')
    api project(path: ':lib_channel')
    implementation project(path: ':libs:jnilame')
    implementation project(path: ':libs:jniopus')
    implementation deps.exoplayer
    implementation 'androidx.lifecycle:lifecycle-runtime:2.3.1'
    implementation deps.accompanist_flowlayout

    // 阿里Arouter方案
    kapt deps.arouter_compiler
    compileOnly deps.arouter_api

    api 'com.github.naman14:TAndroidLame:1.1'

    // 测试依赖
    testImplementation deps.junit
    testImplementation deps.mockk
    testImplementation deps.kotlinx_coroutines_test
    androidTestImplementation deps.androidx_test_junit
    androidTestImplementation deps.androidx_test_espresso_core
}

detekt {
    config = files("../detekt-config.yml")
    buildUponDefaultConfig = true
}